'use client';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useUpdateComplaint } from '../hooks/use-complaints-simple';
import {
  CreateComplaintInput,
  UpdateRepairInformationInput,
  updateRepairInformationSchema
} from '../schemas';

interface RepairInformationFormProps {
  onSuccess: () => void;
  onCancel: () => void;
  complaintId: string;
  initialData?: Partial<CreateComplaintInput>;
}

export function RepairInformationForm({
  onSuccess,
  onCancel,
  complaintId,
  initialData,
}: RepairInformationFormProps) {
  const [proofOfRepairFiles, setProofOfRepairFiles] = useState<File[]>([]);
  const updateComplaintMutation = useUpdateComplaint();
  const t = useTranslations('complaints.form');

  const form = useForm<UpdateRepairInformationInput>({
    resolver: zodResolver(updateRepairInformationSchema),
    defaultValues: {
      // Section A: Basic Information (optional for updates)
      email: initialData?.email,
      date: initialData?.date,
      expected_completion_date: initialData?.expected_completion_date,
      contractor_name: initialData?.contractor_name,
      location: initialData?.location,
      no_pma_lif: initialData?.no_pma_lif,
      description: initialData?.description,
      involves_mantrap: initialData?.involves_mantrap || false,

      // Section B: Repair Information - preserve existing data for editing
      actual_completion_date: initialData?.actual_completion_date || new Date(),
      repair_completion_time: initialData?.repair_completion_time || '',
      cause_of_damage: initialData?.cause_of_damage || '',
      correction_action: initialData?.correction_action || '',
      repair_cost: initialData?.repair_cost || 0,
      proof_of_repair_urls: initialData?.proof_of_repair_urls || [],

      // Preserve current status - don't automatically change to closed
      status: (initialData?.status as 'open' | 'on_hold' | 'closed') || 'closed',
    },
  });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter((file) => {
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
      const isValidType = ['image/jpeg', 'image/png', 'image/jpg'].includes(
        file.type,
      );
      return isValidSize && isValidType;
    });
    setProofOfRepairFiles((prev) => [...prev, ...validFiles]);
  };

  const removeFile = (index: number) => {
    setProofOfRepairFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: UpdateRepairInformationInput) => {
    try {
      console.log('Form submitted with data:', data);
      console.log('Initial data:', initialData);
      console.log('Proof of repair files:', proofOfRepairFiles);

      // Preserve existing data and allow multiple edits
      const updatedData: CreateComplaintInput = {
        // Include existing Section A data from initialData
        email: initialData?.email || '',
        date: initialData?.date || new Date(),
        expected_completion_date: initialData?.expected_completion_date || new Date(),
        contractor_name: initialData?.contractor_name || '',
        location: initialData?.location || '',
        no_pma_lif: initialData?.no_pma_lif || '',
        description: initialData?.description || '',
        involves_mantrap: initialData?.involves_mantrap || false,

        // Section B data from form - updated values
        actual_completion_date: data.actual_completion_date,
        repair_completion_time: data.repair_completion_time,
        cause_of_damage: data.cause_of_damage,
        correction_action: data.correction_action,
        proof_of_repair_urls: data.proof_of_repair_urls,
        repair_cost: data.repair_cost,

        // Preserve current status or set to closed if it's the first time completing Section B
        status: initialData?.status === 'open' || initialData?.status === 'on_hold' ? 'closed' : (initialData?.status || 'closed'),

        // Include proof of repair files
        proofOfRepairFiles,
      };

      console.log('Sending update with data:', updatedData);

      await updateComplaintMutation.mutateAsync({
        id: complaintId,
        data: updatedData,
      });

      console.log('Update successful!');
      onSuccess();
    } catch (error) {
      console.error('Failed to update complaint:', error);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6 max-w-5xl">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {t('title')} - {t('sectionB.title')}
        </h1>
        <p className="text-gray-600">{t('subtitle')}</p>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit, (errors) => {
        console.log('Form validation errors:', errors);
      })} className="space-y-6">
        {/* Section B: Repair Information */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4 bg-green-50">
            <CardTitle className="text-lg font-semibold text-green-800 flex items-center gap-2">
              {t('sectionB.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-4 sm:p-6">
            {/* Actual Completion Date */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {t('sectionB.actualCompletionDate')}
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal h-10',
                      !form.watch('actual_completion_date') && 'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch('actual_completion_date') ? (
                      format(form.watch('actual_completion_date') as Date, 'yyyy-MM-dd')
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={form.watch('actual_completion_date') as Date}
                    onSelect={(date) =>
                      form.setValue('actual_completion_date', date || new Date())
                    }
                  />
                </PopoverContent>
              </Popover>
              {form.formState.errors.actual_completion_date && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.actual_completion_date.message}
                </p>
              )}
            </div>

            {/* Repair Completion Time */}
            <div className="space-y-2">
              <Label htmlFor="repair_completion_time" className="text-sm font-medium text-gray-700">
                {t('sectionB.repairCompletionTime')}
              </Label>
              <Input
                id="repair_completion_time"
                type="time"
                {...form.register('repair_completion_time')}
                className="h-10"
              />
              {form.formState.errors.repair_completion_time && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.repair_completion_time.message}
                </p>
              )}
            </div>

            {/* Cause of Damage */}
            <div className="space-y-2">
              <Label htmlFor="cause_of_damage" className="text-sm font-medium text-gray-700">
                {t('sectionB.causeOfDamage')}
              </Label>
              <Textarea
                id="cause_of_damage"
                {...form.register('cause_of_damage')}
                placeholder="Describe the cause of damage"
                className="min-h-[80px]"
              />
              {form.formState.errors.cause_of_damage && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.cause_of_damage.message}
                </p>
              )}
            </div>

            {/* Correction Action */}
            <div className="space-y-2">
              <Label htmlFor="correction_action" className="text-sm font-medium text-gray-700">
                {t('sectionB.correctionAction')}
              </Label>
              <Textarea
                id="correction_action"
                {...form.register('correction_action')}
                placeholder="Describe the correction action taken"
                className="min-h-[80px]"
              />
              {form.formState.errors.correction_action && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.correction_action.message}
                </p>
              )}
            </div>

            {/* Repair Cost */}
            <div className="space-y-2">
              <Label htmlFor="repair_cost" className="text-sm font-medium text-gray-700">
                {t('sectionB.repairCost')} (RM)
              </Label>
              <Input
                id="repair_cost"
                type="number"
                step="0.01"
                min="0"
                {...form.register('repair_cost', { valueAsNumber: true })}
                className="h-10"
                placeholder="0.00"
              />
              {form.formState.errors.repair_cost && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.repair_cost.message}
                </p>
              )}
            </div>

            {/* Proof of Repair Files */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {t('sectionB.proofOfRepair')}
              </Label>

              {/* Display existing proof of repair URLs */}
              {initialData?.proof_of_repair_urls && initialData.proof_of_repair_urls.length > 0 && (
                <div className="mb-3">
                  <p className="text-xs text-gray-600 mb-2">Previously uploaded files:</p>
                  <div className="space-y-1">
                    {initialData.proof_of_repair_urls.map((url, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-blue-50 rounded text-sm"
                      >
                        <span className="text-blue-700">
                          {url.split('/').pop() || `File ${index + 1}`}
                        </span>
                        <a
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 text-xs"
                        >
                          View
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <Input
                type="file"
                multiple
                accept=".jpg,.jpeg,.png"
                onChange={handleFileUpload}
                className="h-10"
              />
              <p className="text-xs text-gray-500">
                Upload additional photos showing the completed repair (JPG, PNG only, max 10MB each)
              </p>

              {/* Display newly selected files */}
              {proofOfRepairFiles.length > 0 && (
                <div className="mt-2 space-y-1">
                  <p className="text-xs text-gray-600">New files to upload:</p>
                  {proofOfRepairFiles.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
                    >
                      <span>{file.name}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="px-6"
          >
            {t('actions.cancel')}
          </Button>
          <Button
            type="submit"
            className="px-6"
            disabled={updateComplaintMutation.isPending}
            onClick={() => console.log('Submit button clicked', form.formState.errors)}
          >
            {updateComplaintMutation.isPending ? 'Updating...' : t('actions.updateSectionB')}
          </Button>
        </div>
      </form>
    </div>
  );
}
