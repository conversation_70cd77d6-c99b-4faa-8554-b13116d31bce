'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  ComplaintWithRelations,
  useComplaints,
} from '@/features/complaints/hooks/use-complaints-simple';
import { ComplaintUI } from '@/features/complaints/types/ui-types';
import {
  AlertCircle,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Download,
  Edit,
  Plus,
  TrendingUp
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function ComplaintsPage() {
  const router = useRouter();

  // Translations
  const t = useTranslations('complaints');
  // Filter states
  const [filters, setFilters] = useState({
    project: '',
    pmaNo: '',
    admin: '',
    status: '',
    dateRange: ''
  });

  // Fetch real complaints data
  const { data: complaintsData = [], isLoading, error } = useComplaints();
  // Filter function
  const applyFilters = (complaints: ComplaintUI[]) => {
    return complaints.filter((complaint) => {
      const matchesProject = !filters.project || 
        complaint.location?.toLowerCase().includes(filters.project.toLowerCase());
      
      const matchesPmaNo = !filters.pmaNo || 
        complaint.no_pma_lif?.toLowerCase().includes(filters.pmaNo.toLowerCase());
      
      const matchesAdmin = !filters.admin || 
        complaint.email?.toLowerCase().includes(filters.admin.toLowerCase());
      
      const matchesStatus = !filters.status || filters.status === 'all' || 
        complaint.status === filters.status;
      
      return matchesProject && matchesPmaNo && matchesAdmin && matchesStatus;
    });
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const resetFilters = () => {
    setFilters({
      project: '',
      pmaNo: '',
      admin: '',
      status: '',
      dateRange: ''
    });
  };
  // Helper function to determine if Section A is completed
  const isSectionACompleted = (complaint: ComplaintWithRelations): boolean => {
    return !!(
      complaint.email &&
      complaint.date &&
      complaint.expected_completion_date &&
      complaint.contractor_name &&
      complaint.location &&
      complaint.no_pma_lif &&
      complaint.description
    );
  };

  // Helper function to determine if Section B is completed
  const isSectionBCompleted = (complaint: ComplaintWithRelations): boolean => {
    return !!(
      complaint.actual_completion_date &&
      complaint.repair_completion_time &&
      complaint.cause_of_damage &&
      complaint.correction_action &&
      complaint.proof_of_repair_urls &&
      complaint.proof_of_repair_urls.length > 0
    );
  };  // Convert database complaints to UI format
  const rawComplaints: ComplaintUI[] = complaintsData.map((complaint) => {
    const sectionAComplete = isSectionACompleted(complaint);
    const sectionBComplete = isSectionBCompleted(complaint);

    return {
      // Direct database field mapping
      id: complaint.id,
      email: complaint.email,
      number: complaint.number || `DCL-${new Date(complaint.created_at || '').getFullYear()}-${complaint.id.slice(-4)}`,
      date: complaint.date || complaint.created_at || new Date().toISOString(),
      expected_completion_date: complaint.expected_completion_date ||complaint.created_at || new Date().toISOString(),
      contractor_name: complaint.contractor_name,
      location: complaint.location,
      no_pma_lif: complaint.no_pma_lif,
      description: complaint.description,
      involves_mantrap: complaint.involves_mantrap,
      
      // Section B fields
      actual_completion_date: complaint.actual_completion_date,
      repair_completion_time: complaint.repair_completion_time,
      cause_of_damage: complaint.cause_of_damage,
      correction_action: complaint.correction_action,
      proof_of_repair_urls: complaint.proof_of_repair_urls,
      repair_cost: complaint.repair_cost,
      
      // Status and metadata
      status: complaint.status,
      created_at: complaint.created_at,
      
      // Computed fields
      sectionACompleted: sectionAComplete,
      sectionBCompleted: sectionBComplete,
      proofOfRepairFiles:
        complaint.proof_of_repair_urls?.map((url: string, index: number) => ({
          name: `Repair Evidence ${index + 1}`,
          url,
          size: 0, // Size not stored in current schema
        })) || [],
    };
  });
  // Apply filters to get final complaints list
  const complaints = applyFilters(rawComplaints);
  // Calculate stats with database status system
  const submittedReports = complaints.filter(
    (c: ComplaintUI) => c.status === 'open',
  ).length;
  const onHoldReports = complaints.filter(
    (c: ComplaintUI) => c.status === 'on_hold',
  ).length;
  const closedReports = complaints.filter(
    (c: ComplaintUI) => c.status === 'closed',
  ).length;
  const totalReports = complaints.length;const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-800 border-green-200">
            {t('status.open')}
          </Badge>
        );
      case 'on_hold':
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-800 border-orange-200">
            {t('status.onHold')}
          </Badge>
        );
      case 'closed':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-800 border-red-200">
            {t('status.closed')}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }  };
  
  const handleViewComplaint = (complaint: ComplaintUI) => {
    // Navigate to complaint details page or open modal
    router.push(`/complaints/${complaint.id}`);
  };

  const handleEditComplaintFromTable = (complaint: ComplaintUI) => {
    router.push(`/complaints/${complaint.id}/edit`);
  };
  const handleCreateNewComplaint = () => {
    router.push('/complaints/new');
  };

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            Loading complaints...
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex flex-col items-center gap-2 py-8">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <p className="text-red-600">Failed to load complaints</p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : 'Unknown error occurred'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">        
    {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
          <p className="text-gray-600 text-sm mt-1">{t('subtitle')}</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            {t('exportReport')}
          </Button>
          <Button 
            size="sm" 
            className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            onClick={handleCreateNewComplaint}
          >
            <Plus className="h-4 w-4" />
            {t('createAduan')}
          </Button>
        </div>
      </div>      
      {/* Statistics Cards - Matching the design */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gray-50 border-gray-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-700">
                  {submittedReports}
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  {t('stats.submitted')}
                </p>
              </div>
              <div className="p-2 bg-blue-100 rounded-lg">
                <Download className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>        
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-green-700">
                  {closedReports}
                </p>
                <p className="text-sm text-green-600 mt-1">
                  {t('stats.completed')}
                </p>
              </div>
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-orange-50 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-orange-700">
                  {onHoldReports}
                </p>
                <p className="text-sm text-orange-600 mt-1">
                  {t('stats.inProgress')}
                </p>
              </div>
              <div className="p-2 bg-orange-100 rounded-lg">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-purple-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-purple-700">
                  {totalReports}
                </p>
                <p className="text-sm text-purple-600 mt-1">
                  {t('stats.totalReports')}
                </p>
              </div>
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>     
       {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">{t('charts.weeklyTrend')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-48 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">{t('charts.chartImplementation')}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">{t('charts.statusDistribution')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">{t('status.resolved')}</span>
                </div>
                <span className="text-sm font-medium">49%</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-sm">{t('status.inProgress')}</span>
                </div>
                <span className="text-sm font-medium">31%</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm">{t('status.open')}</span>
                </div>
                <span className="text-sm font-medium">20%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>     
       {/* Filter Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">{t('filters.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('filters.project')}</label>
              <Input 
                placeholder={t('filters.projectPlaceholder')}
                value={filters.project}
                onChange={(e) => handleFilterChange('project', e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('filters.pmaNo')}</label>
              <Input 
                placeholder={t('filters.pmaNoPlaceholder')}
                value={filters.pmaNo}
                onChange={(e) => handleFilterChange('pmaNo', e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('filters.admin')}</label>
              <Input 
                placeholder={t('filters.adminPlaceholder')}
                value={filters.admin}
                onChange={(e) => handleFilterChange('admin', e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('filters.status')}</label>
              <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.statusPlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allStatus')}</SelectItem>
                  <SelectItem value="open">{t('status.open')}</SelectItem>
                  <SelectItem value="in_progress">{t('status.inProgress')}</SelectItem>
                  <SelectItem value="pending_approval">{t('status.pendingApproval')}</SelectItem>
                  <SelectItem value="verified">{t('status.verified')}</SelectItem>
                  <SelectItem value="closed">{t('status.closed')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex justify-end mt-4">
            <Button variant="outline" size="sm" onClick={resetFilters}>
              {t('filters.resetFilters')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-6">        {/* Aduan Reports Table */}
        <Card>
          <CardHeader className="pb-4">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg font-semibold">
                {t('table.title')}
              </CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  {t('exportReport')}
                </Button>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-medium text-gray-700">
                    {t('table.reportId')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.dateSubmitted')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.pmaNumber')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.location')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.issueSummary')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.status')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.completionDate')}
                  </TableHead>
                  <TableHead className="font-medium text-gray-700">
                    {t('table.actions')}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {complaints.slice(0, 5).map((complaint: ComplaintUI) => (                  
                  <TableRow key={complaint.id} className="hover:bg-gray-50">
                    <TableCell>
                      <Button
                        variant="link"
                        className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium"
                        onClick={() => handleViewComplaint(complaint)}
                      >
                        {complaint.number}
                      </Button>
                    </TableCell>
                    <TableCell className="text-sm text-gray-600">
                      {new Date(complaint.date).toLocaleDateString('en-MY')}
                    </TableCell>
                    <TableCell className="text-sm">
                      <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                        {complaint.no_pma_lif || 'N/A'}
                      </code>
                    </TableCell>
                    <TableCell className="text-sm text-gray-600">
                      {complaint.location}
                    </TableCell>
                    <TableCell className="text-sm text-gray-600 max-w-xs truncate">
                      {complaint.description || 'No description'}
                    </TableCell>
                    <TableCell>{getStatusBadge(complaint.status)}</TableCell>
                    <TableCell className="text-sm text-gray-600">
                      {complaint.actual_completion_date 
                        ? new Date(complaint.actual_completion_date).toLocaleDateString('en-MY')
                        : '-'
                      }
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => handleEditComplaintFromTable(complaint)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
              {/* Pagination */}
            <div className="flex justify-between items-center p-4 border-t">
              <span className="text-sm text-gray-600">
                {t('pagination.showing', { start: 1, end: Math.min(5, complaints.length), total: complaints.length })}
              </span>
              <div className="flex gap-1">
                <Button variant="outline" size="sm">
                  <ChevronLeft className="h-4 w-4" />
                  {t('pagination.previous')}
                </Button>
                <Button variant="outline" size="sm" className="bg-blue-600 text-white">
                  1
                </Button>
                <Button variant="outline" size="sm">
                  2
                </Button>
                <Button variant="outline" size="sm">
                  3
                </Button>
                <Button variant="outline" size="sm">
                  {t('pagination.next')}
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}